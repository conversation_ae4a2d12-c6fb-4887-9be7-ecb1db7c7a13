import React, { useState, useEffect } from 'react';
import { Message } from '../types';
import GameInterventionsComponent from '../../../components/games/GameInterventionsComponent';
import InterventionsFromIdsComponent from '../../../components/interventions/InterventionsFromIdsComponent';
import { useParams } from 'react-router-dom';

interface ChatMessageProps {
  message: Message;
  studentId?: number; // Added optional studentId prop
}

export const ChatMessage: React.FC<ChatMessageProps> = ({ message, studentId }) => {
  // Get student ID from URL parameters if not passed as a prop
  const params = useParams<{ id?: string }>();
  const effectiveStudentId = studentId || (params.id ? parseInt(params.id, 10) : undefined);
  const [dots, setDots] = useState('.');

  // Effect for dots animation
  useEffect(() => {
    if (message.isStreaming) {
      const interval = setInterval(() => {
        setDots(prev => {
          if (prev === '.') return '..';
          if (prev === '..') return '...';
          return '.';
        });
      }, 500);

      return () => clearInterval(interval);
    }
  }, [message.isStreaming]);

  return (
    <div className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'} mb-4`}>
      {message.sender === 'system' && (
        <div className="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center mr-2">
          <span className="text-sm font-semibold">AI</span>
        </div>
      )}

      <div
        className={`max-w-[70%] rounded-lg p-3 ${
          message.sender === 'user'
            ? 'bg-blue-50 text-black border-2 border-primaryBtn'
            : 'bg-gray-100 text-gray-900 border border-gray-200'
        }`}
      >
        <div className="whitespace-pre-wrap">
          {message.text ? message.text : message.isStreaming ? dots : ''}
        </div>

        {/* Display InterventionsFromIdsComponent when message contains interventions */}
        {message.data &&
          typeof message.data === 'object' &&
          'interventions' in message.data &&
          Array.isArray(message.data.interventions) &&
          message.data.interventions.length > 0 && (
            <div className="mt-4">
              <InterventionsFromIdsComponent
                interventions={message.data.interventions}
                gradeLevel="1st grade"
              />
            </div>
          )}

        {/* Display GameInterventionsComponent when message contains game data (old format) */}
        {message.data &&
          typeof message.data === 'object' &&
          'games' in message.data &&
          Array.isArray(message.data.games) &&
          message.data.games.length > 0 && (
            <div className="mt-4">
              <GameInterventionsComponent
                areaId={(() => {
                  const sanitized = String(message.data.games[0].domain || '')
                    .toLowerCase()
                    .replace(/\s+/g, '-')
                    .replace(/[&]/g, 'and')
                    .replace(/[^a-z0-9-]/g, '');
                  return sanitized || 'default-area';
                })()}
                areaName={String(message.data.games[0].domain || 'Default area')}
                gradeLevel="1st grade"
                studentId={effectiveStudentId}
              />
            </div>
          )}
      </div>

      {message.sender === 'user' && (
        <div className="w-8 h-8 rounded-full bg-primaryBtn flex items-center justify-center ml-2">
          <span className="text-sm font-semibold text-white">U</span>
        </div>
      )}
    </div>
  );
};

export default ChatMessage;

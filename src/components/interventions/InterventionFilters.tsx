import React, { useState } from 'react';
import { Input } from '../ui/Input';
import { Select } from '../ui/Select';
import { useInterventionFilterStore } from '../../store/interventionFilterStore';

interface InterventionFiltersProps {
  className?: string;
}

export const InterventionFilters: React.FC<InterventionFiltersProps> = ({ className = '' }) => {
  const {
    filters,
    availableOptions,
    setSearchQuery,
    toggleCategory,
    toggleDifficultyLevel,
    toggleTargetAgeGroup,
    clearAllFilters,
  } = useInterventionFilterStore();

  const [showAllCategories, setShowAllCategories] = useState(false);
  const [showAllAgeGroups, setShowAllAgeGroups] = useState(false);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  const handleClearFilters = () => {
    clearAllFilters();
  };

  const hasActiveFilters = 
    filters.searchQuery ||
    filters.selectedCategories.length > 0 ||
    filters.selectedDifficultyLevels.length > 0 ||
    filters.selectedTargetAgeGroups.length > 0;

  // Show only first 5 categories by default
  const displayedCategories = showAllCategories 
    ? availableOptions.categories 
    : availableOptions.categories.slice(0, 5);

  // Show only first 3 age groups by default
  const displayedAgeGroups = showAllAgeGroups
    ? availableOptions.targetAgeGroups
    : availableOptions.targetAgeGroups.slice(0, 3);

  return (
    <div className={`bg-white rounded-xl shadow-sm border border-gray-200 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Filter Interventions</h3>
        {hasActiveFilters && (
          <button
            onClick={handleClearFilters}
            className="text-sm text-blue-600 hover:text-blue-700 font-medium"
          >
            Clear All
          </button>
        )}
      </div>

      <div className="space-y-6">
        {/* Search Input */}
        <div>
          <Input
            type="text"
            placeholder="Search interventions by name, description, or objective..."
            value={filters.searchQuery}
            onChange={handleSearchChange}
            className="w-full"
          />
        </div>

        {/* Categories Filter */}
        {availableOptions.categories.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Categories
            </label>
            <div className="space-y-2">
              {displayedCategories.map(category => (
                <label key={category} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.selectedCategories.includes(category)}
                    onChange={() => toggleCategory(category)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700 capitalize">
                    {category}
                  </span>
                </label>
              ))}
              {availableOptions.categories.length > 5 && (
                <button
                  onClick={() => setShowAllCategories(!showAllCategories)}
                  className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                >
                  {showAllCategories ? 'Show Less' : `Show All (${availableOptions.categories.length})`}
                </button>
              )}
            </div>
          </div>
        )}

        {/* Difficulty Level Filter */}
        {availableOptions.difficultyLevels.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Difficulty Level
            </label>
            <div className="space-y-2">
              {availableOptions.difficultyLevels.map(level => (
                <label key={level} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.selectedDifficultyLevels.includes(level)}
                    onChange={() => toggleDifficultyLevel(level)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700 capitalize">
                    {level}
                  </span>
                </label>
              ))}
            </div>
          </div>
        )}

        {/* Target Age Group Filter */}
        {availableOptions.targetAgeGroups.length > 0 && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Target Age Group
            </label>
            <div className="space-y-2">
              {displayedAgeGroups.map(ageGroup => (
                <label key={ageGroup} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={filters.selectedTargetAgeGroups.includes(ageGroup)}
                    onChange={() => toggleTargetAgeGroup(ageGroup)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    {ageGroup}
                  </span>
                </label>
              ))}
              {availableOptions.targetAgeGroups.length > 3 && (
                <button
                  onClick={() => setShowAllAgeGroups(!showAllAgeGroups)}
                  className="text-sm text-blue-600 hover:text-blue-700 font-medium"
                >
                  {showAllAgeGroups ? 'Show Less' : `Show All (${availableOptions.targetAgeGroups.length})`}
                </button>
              )}
            </div>
          </div>
        )}

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div className="pt-4 border-t border-gray-200">
            <div className="flex flex-wrap gap-2">
              {filters.searchQuery && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Search: "{filters.searchQuery}"
                </span>
              )}
              {filters.selectedCategories.map(category => (
                <span
                  key={category}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                >
                  {category}
                  <button
                    onClick={() => toggleCategory(category)}
                    className="ml-1 text-green-600 hover:text-green-800"
                  >
                    ×
                  </button>
                </span>
              ))}
              {filters.selectedDifficultyLevels.map(level => (
                <span
                  key={level}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
                >
                  {level}
                  <button
                    onClick={() => toggleDifficultyLevel(level)}
                    className="ml-1 text-yellow-600 hover:text-yellow-800"
                  >
                    ×
                  </button>
                </span>
              ))}
              {filters.selectedTargetAgeGroups.map(ageGroup => (
                <span
                  key={ageGroup}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800"
                >
                  {ageGroup}
                  <button
                    onClick={() => toggleTargetAgeGroup(ageGroup)}
                    className="ml-1 text-purple-600 hover:text-purple-800"
                  >
                    ×
                  </button>
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

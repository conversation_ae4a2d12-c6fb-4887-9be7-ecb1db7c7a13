import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Modal } from './Modal';
import successImage from '../../assets/image-success.svg';

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
  onContinue?: () => void;
  message: string;
  primaryButtonText?: string;
  secondaryButtonText?: string;
}

export const SuccessModal: React.FC<SuccessModalProps> = ({
  isOpen,
  onClose,
  onContinue,
  message,
  primaryButtonText = 'Go to the Dashboard',
  secondaryButtonText = 'Close and back to chat',
}) => {
  const navigate = useNavigate();

  const handleContinue = () => {
    if (onContinue) {
      onContinue();
    }
    onClose();
    navigate('/dashboard');
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} hideCloseButton>
      <div className="flex flex-col items-center justify-center w-full max-w-md mx-auto bg-white rounded-2xl p-8 text-center">
        <div className="w-40 h-40 mb-8">
          <img src={successImage} alt="Success" className="w-full h-full object-contain" />
        </div>

        <h2 className="text-3xl font-bold text-gray-900 mb-6">Success!</h2>
        <p className="text-gray-600 text-lg mb-8 px-4">{message}</p>

        <div className="flex flex-col space-y-4 w-full max-w-xs">
          {onContinue && (
            <button
              onClick={handleContinue}
              className="w-full py-4 bg-[#005773] text-white rounded-xl hover:bg-[#004a63] transition-colors text-base font-medium"
            >
              {primaryButtonText}
            </button>
          )}
          <button
            onClick={onClose}
            className="w-full py-4 bg-white text-[#005773] border border-[#005773] rounded-xl hover:bg-gray-50 transition-colors text-base font-medium"
          >
            {secondaryButtonText}
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default SuccessModal;

import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { authService, RegisterData } from '../../services/authService';
import { consentService, VerificationMethod } from '../../services/consentService';
import { Role, USER_ROLES, DEFAULT_ROLES } from '../../constants/auth';
import { FloatingInput } from '../../components/ui/FloatingInput';
import { Button } from '../../components/Button/Button';
import { Logo } from '../../components/Logo/Logo';
import teacher from '../../assets/teacher.jpg';

interface ApiResponse {
  message: string;
  next_step?: string;
}

interface ApiErrorResponse {
  response?: {
    data?: {
      detail?:
        | string
        | Array<{
            type: string;
            loc: string[];
            msg: string;
            input: string;
            ctx: Record<string, unknown>;
          }>;
      message?: string;
    };
  };
  message?: string;
}

export const RegisterPage: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<RegisterData>({
    first_name: '',
    last_name: '',
    email: '',
    password: '',
    role_id: 1,
    age: undefined,
  });
  const [parentEmail, setParentEmail] = useState('');
  const [roles, setRoles] = useState<Role[]>(DEFAULT_ROLES);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [verificationStep, setVerificationStep] = useState<VerificationMethod | null>(null);
  const [verificationResponse, setVerificationResponse] = useState<{
    message: string;
    verification_url?: string;
    redirect_to?: string;
  } | null>(null);

  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const rolesData = await authService.getRoles();
        if (Array.isArray(rolesData) && rolesData.length > 0) {
          setRoles(rolesData);
        }
      } catch {
        setRoles(DEFAULT_ROLES);
      }
    };

    fetchRoles();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);
    setVerificationStep(null);
    setVerificationResponse(null);

    try {
      const response = (await authService.register(formData)) as ApiResponse;
      setLoading(false);

      if (response?.message === 'Registration successful.') {
        setSuccess(true);
        setTimeout(() => {
          navigate('/login');
        }, 3000);
      } else if (response?.message === 'Parental consent required for users under 13 years old.') {
        try {
          const consentResponse = await consentService.chooseMethod({
            child_email: formData.email,
            parent_email: parentEmail,
            verification_method: 'email',
          });
          setVerificationStep('email');
          setVerificationResponse(consentResponse);
          setError(
            'Parental consent required for users under 13 years old. Choose verification method:'
          );
        } catch (consentErr: unknown) {
          const error = consentErr as Error;
          setError(error.message || 'Error occurred while selecting verification method.');
        }
      }
    } catch (err: unknown) {
      const apiError = err as ApiErrorResponse;

      if (apiError.response?.data) {
        const errorData = apiError.response.data;

        if (errorData.detail === 'A user with this email already exists.') {
          setError('A user with this email already exists.');
        } else if (Array.isArray(errorData.detail) && errorData.detail.length > 0) {
          setError(errorData.detail[0].msg || 'Registration failed. Please try again.');
        } else {
          setError(errorData.message || 'Registration failed. Please try again.');
        }
      } else {
        setError(apiError.message || 'Registration failed. Please try again.');
      }

      setLoading(false);
    }
  };

  const handleVerificationMethod = async (method: VerificationMethod) => {
    setVerificationStep(method);
    setLoading(true);
    setError(null);
    setVerificationResponse(null);

    try {
      const response = await consentService.chooseMethod({
        child_email: formData.email,
        parent_email: parentEmail,
        verification_method: method,
      });

      setVerificationResponse(response);

      if (response.redirect_to) {
        if (method === 'credit_card') {
          navigate(response.redirect_to);
        } else if (method === 'pdf_upload') {
          navigate(response.redirect_to);
        }
      }
    } catch (err: unknown) {
      const error = err as Error;
      setError(error.message || 'An error occurred while selecting the verification method.');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]:
        name === 'role_id'
          ? parseInt(value)
          : name === 'age'
            ? value === ''
              ? undefined
              : parseInt(value)
            : value,
    }));
  };

  return (
    <div className="flex min-h-screen w-full">
      <div className="hidden md:block md:w-2/3 bg-gray-100">
        <div className="relative w-full h-full">
          <div className="absolute top-8 left-8 z-10">
            <Logo />
          </div>
          <img
            src={teacher}
            alt="Teacher smiling"
            className="absolute top-1/2 -left-1/2 h-full object-cover translate-x-1/2 -translate-y-1/2"
          />
        </div>
      </div>

      <div className="w-full md:w-1/3 flex flex-col justify-center items-center p-6 overflow-y-auto">
        <div className="w-full max-w-md">
          <div className="mb-8">
            <div className="text-sm font-medium text-gray-500 mb-2">CREATE YOUR ACCOUNT</div>
            <h1 className="text-3xl font-bold text-gray-900">Join YUBU</h1>
          </div>

          <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div className="space-y-4">
              <FloatingInput
                id="first_name"
                name="first_name"
                type="text"
                label="First Name"
                required
                value={formData.first_name}
                onChange={handleChange}
              />

              <FloatingInput
                id="last_name"
                name="last_name"
                type="text"
                label="Last Name"
                required
                value={formData.last_name}
                onChange={handleChange}
              />

              <FloatingInput
                id="email"
                name="email"
                type="email"
                label="Email address"
                required
                value={formData.email}
                onChange={handleChange}
              />

              <FloatingInput
                id="password"
                name="password"
                type="password"
                label="Password"
                required
                value={formData.password}
                onChange={handleChange}
              />

              <FloatingInput
                id="age"
                name="age"
                type="number"
                label="Age"
                required
                value={formData.age !== undefined ? formData.age.toString() : ''}
                onChange={handleChange}
              />

              <div className="relative z-0 w-full mb-5 group">
                <select
                  id="role_id"
                  name="role_id"
                  required
                  value={formData.role_id}
                  onChange={handleChange}
                  className="block py-2.5 px-0 w-full text-sm text-gray-900 bg-transparent border-0 border-b-2 border-gray-300 appearance-none focus:outline-none focus:ring-0 focus:border-primaryBtn peer"
                >
                  {roles.map(role => (
                    <option key={role.id} value={role.id}>
                      {role.name}
                    </option>
                  ))}
                </select>
                <label
                  htmlFor="role_id"
                  className="peer-focus:font-medium absolute text-sm text-gray-500 duration-300 transform -translate-y-6 scale-75 top-3 -z-10 origin-[0] peer-focus:start-0 peer-focus:text-primaryBtn peer-placeholder-shown:scale-100 peer-placeholder-shown:translate-y-0 peer-placeholder-shown:z-[-10] peer-focus:scale-75 peer-focus:z-[10] peer-focus:-translate-y-6 peer-valid:z-[10]"
                >
                  Role
                </label>
              </div>

              {verificationStep === null &&
                formData.role_id === USER_ROLES.STUDENT &&
                formData.age !== undefined &&
                formData.age < 13 && (
                  <FloatingInput
                    id="parent_email"
                    name="parent_email"
                    type="email"
                    label="Parent's Email"
                    required
                    value={parentEmail}
                    onChange={e => setParentEmail(e.target.value)}
                  />
                )}
            </div>

            {error && (
              <div className="space-y-4">
                <div className="text-red-500 text-sm text-center">{error}</div>
                {verificationStep && !verificationResponse && (
                  <div className="flex flex-col items-center space-y-4">
                    <p className="text-sm text-gray-600">Choose verification method:</p>
                    <div className="flex justify-center space-x-4">
                      <button
                        type="button"
                        onClick={() => handleVerificationMethod('email')}
                        className={`px-4 py-2 rounded-md text-sm font-medium ${
                          verificationStep === 'email'
                            ? 'bg-[#005773] cursor-pointer text-white'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        Email
                      </button>
                      <button
                        type="button"
                        onClick={() => handleVerificationMethod('credit_card')}
                        className={`px-4 py-2 rounded-md text-sm font-medium ${
                          verificationStep === 'credit_card'
                            ? 'bg-[#005773] cursor-pointer text-white'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        Credit Card
                      </button>
                      <button
                        type="button"
                        onClick={() => handleVerificationMethod('pdf_upload')}
                        className={`px-4 py-2 rounded-md text-sm font-medium ${
                          verificationStep === 'pdf_upload'
                            ? 'bg-[#005773] cursor-pointer text-white'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        PDF
                      </button>
                    </div>
                  </div>
                )}
                {verificationResponse && (
                  <div className="text-sm text-center">
                    <p className="text-green-600 mb-2">{verificationResponse.message}</p>
                    {verificationResponse.verification_url && (
                      <p className="text-gray-600">
                        Verification link has been sent to parent&apos;s email.
                      </p>
                    )}
                  </div>
                )}
              </div>
            )}

            {success && (
              <div className="text-green-500 text-sm text-center animate-fade-in">
                Registered Successfully! Redirecting to login page...
              </div>
            )}

            <Button
              type="submit"
              fullWidth
              disabled={loading || success || (verificationStep !== null && !verificationResponse)}
            >
              {loading ? 'Processing...' : success ? 'Registered!' : 'CREATE ACCOUNT'}
            </Button>

            <div className="text-center mt-4">
              <p className="text-sm text-gray-600">
                Already have an account?{' '}
                <Link to="/login" className="text-black font-medium underline">
                  SIGN IN HERE
                </Link>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

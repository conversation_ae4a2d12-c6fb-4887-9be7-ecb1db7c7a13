import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useParams } from 'react-router-dom';
import { chatService, ChatStreamPayload } from '../../services/chatService';
import { Student } from '../../services/studentService';
import stars from '../../assets/stars.svg';

import {
  ChatPageProps,
  EntryType,
  Message,
  ReadingAreaDetails,
  ReadingArea,
  AnsweredQuestion,
  assessmentQuestions,
} from './types';

import ChatMessage from './components/ChatMessage';
import ReadingAreaSelector from './components/ReadingAreaSelector';
import ChatInput from './components/ChatInput';
import PredefinedPrompts from './components/PredefinedPrompts';

import useChatWebSocket from './hooks/useChatWebSocket';
import {
  handleExercisesSubmit,
  processResponseChunk,
  GameItem,
  detectGamesInText,
  InterventionItem,
  detectInterventionsInText,
} from './utils/chatUtils';
import AssessmentDetailsComponent from '../../components/AssessmentDetailsComponent';
import { AssessmentResult } from '../../types/assessment';

interface ChatHistoryItem extends Omit<Message, 'text' | 'sender' | 'type'> {
  user_question?: string;
  assistant?: string;
  text?: string;
  sender?: 'user' | 'system' | 'bot' | 'bot_partial';
  type?: 'text' | 'survey' | 'assessment' | 'support';
}

export const ChatPage: React.FC<ChatPageProps> = ({
  studentId: propStudentId,
  studentName: propStudentName,
}) => {
  const params = useParams<{ id?: string; chatId?: string }>();
  const userId = propStudentId || params.id;
  const chatId = params.chatId || '1';
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);
  const [studentData, setStudentData] = useState<Partial<Student> | null>(null);
  const [loadingStudent, setLoadingStudent] = useState<boolean>(true);

  useEffect(() => {
    const checkAndCreateSession = async () => {
      try {
        setLoading(true);
        // Pobierz listę istniejących sesji
        const sessions = await chatService.getSessions();

        // Sprawdź, czy istnieje już sesja dla tego użytkownika
        const userSessions = sessions
          .filter(session => session.student_id === Number(userId))
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

        if (userSessions.length > 0) {
          // Use the latest session
          const latestSession = userSessions[0];

          setCurrentSessionId(latestSession.session_id);

          try {
            const chatHistory = await chatService.getChatHistory(latestSession.session_id);

            if (Array.isArray(chatHistory)) {
              // Process chat history while preserving game data
              const formattedMessages = processMessageHistory(chatHistory as ChatHistoryItem[]);

              // Set formatted messages
              setMessages(formattedMessages);
            }
          } catch (historyError) {
            // console.error('Error while fetching chat history:', historyError);
          }
        } else if (userId) {
          // If there's no session, create a new one

          const newSessionId = await chatService.startSession(userId);
          if (newSessionId) {
            setCurrentSessionId(newSessionId);
          }
        }
      } catch (error) {
        // console.error('Error while managing chat sessions:', error);
        // In case of error, use chatId as fallback
        setCurrentSessionId(chatId);
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      checkAndCreateSession();
      // Note: getStudent API removed - using student ID from URL
      setLoadingStudent(true);
      setStudentData({
        id: Number(userId),
        first_name: 'Student',
        last_name: `#${userId}`,
        age: 0,
        gender: '',
        email: '',
        password: '',
        role_id: 0,
      });
      setLoadingStudent(false);
    }
  }, [userId, chatId]);

  // Stan wiadomości i czatu
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [info, setInfo] = useState<string | null>(null);
  const [selectedEntry, setSelectedEntry] = useState<EntryType | null>(null);
  const [showChat, setShowChat] = useState(false);

  // Referencje do elementów DOM
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const areaSelectorRef = useRef<HTMLDivElement>(null);
  const assessmentResultRef = useRef<HTMLDivElement>(null);

  // Stan dla wyboru poziomu klasy
  const [showGradeSelector, setShowGradeSelector] = useState(false);
  const [selectedGrade, setSelectedGrade] = useState<string>('1st grade');
  const [gradeSelectorText, setGradeSelectorText] = useState<string>('');
  const [displayGradeText, setDisplayGradeText] = useState<string>('');
  const [gradeCharIndex, setGradeCharIndex] = useState<number>(0);
  const [hasUserSelectedGrade, setHasUserSelectedGrade] = useState(false);

  // Stan dla obszarów czytania
  const [readingAreas, setReadingAreas] = useState<ReadingArea[]>([]);
  const [loadingReadingAreas, setLoadingReadingAreas] = useState(false);
  const [errorReadingAreas, setErrorReadingAreas] = useState<string | null>(null);
  const [areaCharIndex, setAreaCharIndex] = useState(0);
  const [displayAreaText, setDisplayAreaText] = useState('');
  const [areaSelectorText, setAreaSelectorText] = useState('');
  const [selectedReadingAreas, setSelectedReadingAreas] = useState<ReadingArea[]>([]);
  const [selectedAreaDetails, setSelectedAreaDetails] = useState<ReadingAreaDetails | null>(null);
  const [loadingAreaDetails, setLoadingAreaDetails] = useState<boolean>(false);

  // Stan dla oceny (assessment)
  const [showAssessment, setShowAssessment] = useState(false);
  const [assessmentStep, setAssessmentStep] = useState(0);
  const [assessmentText, setAssessmentText] = useState('');
  const [assessmentCharIndex, setAssessmentCharIndex] = useState(0);
  const [showAssessmentButtons, setShowAssessmentButtons] = useState(false);
  const [displayAssessmentText, setDisplayAssessmentText] = useState('');
  const [answeredQuestions, setAnsweredQuestions] = useState<AnsweredQuestion[]>([]);
  const [currentQuestionAnimating, setCurrentQuestionAnimating] = useState(true);
  const [assessmentResult, setAssessmentResult] = useState<AssessmentResult | null>(null);

  // Stan dla ekranu powitalnego
  const [welcomeText, setWelcomeText] = useState('');
  const [showButtons, setShowButtons] = useState(false);

  // Referencja do stanu otrzymania wystarczającej ilości treści
  const hasReceivedEnoughContentRef = useRef<boolean>(false);

  // Funkcja pomocnicza do przetwarzania odpowiedzi asystenta i wykrywania gier/interwencji
  const processAssistantResponse = useCallback(
    (
      text: string
    ): { text: string; data?: { games?: GameItem[]; interventions?: InterventionItem[] } } => {
      // Najpierw sprawdź nowy format z interwencjami
      const detectedInterventions = detectInterventionsInText(text);
      if (detectedInterventions) {
        // Konwertujemy interventions na InterventionItem[] jeśli potrzeba
        let interventions: InterventionItem[] | undefined;
        if (detectedInterventions.interventions) {
          if (
            Array.isArray(detectedInterventions.interventions) &&
            detectedInterventions.interventions.length > 0
          ) {
            if (typeof detectedInterventions.interventions[0] === 'string') {
              interventions = (detectedInterventions.interventions as string[]).map(id => ({
                intervention_id: id,
              }));
            } else {
              interventions = detectedInterventions.interventions as InterventionItem[];
            }
          }
        }

        return {
          text: detectedInterventions.text || '',
          data: { interventions },
        };
      }

      // Check if response contains array of games (old format)
      const detectedGames = detectGamesInText(text);
      if (detectedGames) {
        return {
          text: '',
          data: { games: detectedGames },
        };
      }

      // If neither interventions nor games detected, return original text
      return { text };
    },
    []
  );

  // Function to process message history
  const processMessageHistory = useCallback(
    (chatHistory: ChatHistoryItem[]): Message[] => {
      const formattedMessages: Message[] = [];

      chatHistory.forEach((item, index) => {
        // Add user message
        if (item.user_question) {
          formattedMessages.push({
            id: Date.now() - (chatHistory.length - index) * 1000,
            text: item.user_question,
            sender: 'user',
            timestamp: new Date(Date.now() - (chatHistory.length - index) * 1000).toISOString(),
            type: 'text',
          });
        }

        // Add assistant response
        if (item.assistant) {
          // Process assistant response and detect games
          const processedResponse = processAssistantResponse(item.assistant);

          formattedMessages.push({
            id: Date.now() - (chatHistory.length - index) * 1000 + 500,
            text: processedResponse.text,
            sender: 'bot',
            timestamp: new Date(
              Date.now() - (chatHistory.length - index) * 1000 + 500
            ).toISOString(),
            type: 'text',
            data: processedResponse.data,
          });
        }
      });

      return formattedMessages;
    },
    [processAssistantResponse]
  );

  // Obsługa WebSocket
  const handleIncomingMessage = useCallback(
    (incomingMessage: Message) => {
      setMessages(prevMessages => {
        if (prevMessages.length === 0) {
          return [incomingMessage];
        }

        const lastMessage = prevMessages[prevMessages.length - 1];

        if (
          lastMessage.sender === 'system' &&
          incomingMessage.sender === 'system' &&
          !prevMessages.some(
            (msg, idx) =>
              idx > prevMessages.length - 3 &&
              idx < prevMessages.length - 1 &&
              msg.sender === 'user'
          )
        ) {
          const updatedMessages = [...prevMessages];
          updatedMessages[prevMessages.length - 1] = {
            ...lastMessage,
            text: lastMessage.text + '\n' + incomingMessage.text,
            timestamp: incomingMessage.timestamp,
          };
          return updatedMessages;
        } else {
          return [...prevMessages, incomingMessage];
        }
      });

      if (
        incomingMessage.sender === 'system' &&
        incomingMessage.text.includes('Hello! I am Yubu')
      ) {
        setHasUserSelectedGrade(false);
      }

      setLoading(false);
      scrollToBottom();
    },
    [setMessages, setLoading, setHasUserSelectedGrade]
  );

  const { isWsConnected, connectToWebSocket, disconnectWebSocket } = useChatWebSocket({
    userId,
    chatId,
    onMessageReceived: handleIncomingMessage,
  });

  // Inicjalizacja połączenia WebSocket po zamontowaniu komponentu
  useEffect(() => {
    if (userId && chatId) {
      connectToWebSocket();
    }

    // Zamknij połączenie przy odmontowaniu komponentu
    return () => {
      disconnectWebSocket();
    };
  }, [userId, chatId, connectToWebSocket, disconnectWebSocket]);

  // Funkcja do przewijania do dołu listy wiadomości
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Efekt dla animacji tekstu powitalnego
  useEffect(() => {
    if (messages.length === 0 && !showChat) {
      const fullText =
        'I am Assistant, how can I help you. Choose the area of what do you want to help with?';

      setWelcomeText('');
      setShowButtons(false);

      let index = 0;

      const animateText = () => {
        if (index < fullText.length) {
          setWelcomeText(fullText.substring(0, index + 1));
          index++;
          setTimeout(animateText, 25);
        } else {
          setTimeout(() => setShowButtons(true), 2000);
        }
      };

      setTimeout(animateText, 200);
    }
  }, [messages.length, showChat]);

  // Efekt dla przewijania do dołu po dodaniu nowych wiadomości
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  // Efekt dla animacji tekstu wyboru poziomu klasy
  useEffect(() => {
    if (showGradeSelector && gradeSelectorText && gradeCharIndex < gradeSelectorText.length) {
      const timer = setTimeout(() => {
        setDisplayGradeText(prev => prev + gradeSelectorText[gradeCharIndex]);
        setGradeCharIndex(prev => prev + 1);
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [showGradeSelector, gradeSelectorText, gradeCharIndex]);

  // Efekt dla animacji tekstu wyboru obszaru czytania
  useEffect(() => {
    if (areaSelectorText && areaCharIndex < areaSelectorText.length) {
      const timer = setTimeout(() => {
        setDisplayAreaText(prev => prev + areaSelectorText[areaCharIndex]);
        setAreaCharIndex(prev => prev + 1);
        // Usunięto console.log
      }, 20);
      return () => clearTimeout(timer);
    } else if (areaCharIndex === areaSelectorText.length && areaSelectorText) {
      setTimeout(() => {
        if (areaSelectorRef.current) {
          areaSelectorRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        }
      }, 300);
    }
  }, [areaSelectorText, areaCharIndex]);

  // Efekt dla ustawienia tekstu wyboru obszaru po wybraniu poziomu klasy
  useEffect(() => {
    if (selectedGrade && hasUserSelectedGrade) {
      setDisplayAreaText('');
      setAreaCharIndex(0);
      setAreaSelectorText('Choose the area of reading you want help with:');
    }
  }, [selectedGrade, hasUserSelectedGrade]);

  // Efekt dla ładowania obszarów czytania po wybraniu poziomu klasy
  useEffect(() => {
    if (selectedGrade && showGradeSelector && !hasUserSelectedGrade) {
      setLoadingReadingAreas(true);
      setErrorReadingAreas(null);
      chatService
        .getReadingAreas(selectedGrade)
        .then(data => {
          const areas = data.reading_areas || [];

          // Dodaj opcję "Math Problem" do listy obszarów czytania
          const mathProblemOption = {
            id: 999, // Unikalny ID dla opcji matematycznej
            name: 'Math Problem',
            description: 'Exercises for practicing math concepts and problem-solving skills.',
          };

          const updatedAreas = [...areas, mathProblemOption];
          setReadingAreas(updatedAreas);
        })
        .catch(() => {
          // console.error('Error fetching reading areas:', err);
          setErrorReadingAreas('Failed to load reading areas. Please try again.');
        })
        .finally(() => {
          setLoadingReadingAreas(false);
        });
    }
  }, [selectedGrade, showGradeSelector, hasUserSelectedGrade]);

  // Efekt dla zmiany wybranego typu wejścia
  useEffect(() => {
    setError(null);
    setLoading(false);
    setLoadingReadingAreas(false);
    setLoadingAreaDetails(false);
    setSelectedAreaDetails(null);

    setShowGradeSelector(false);
    setShowAssessment(false);

    setAssessmentStep(0);
    setAssessmentText('');
    setDisplayAssessmentText('');
    setAssessmentCharIndex(0);
    setShowAssessmentButtons(false);
    setAnsweredQuestions([]);
    setCurrentQuestionAnimating(true);

    setGradeSelectorText('');
    setDisplayGradeText('');
    setGradeCharIndex(0);
    setReadingAreas([]);
    setSelectedReadingAreas([]);
    setAreaSelectorText('');
    setDisplayAreaText('');
    setAreaCharIndex(0);

    if (selectedEntry !== null) {
      setMessages([]);
    }
    setMessage('');

    if (selectedEntry === 'know') {
      setInfo('You selected: I Know What I Need');
      setShowGradeSelector(true);
      setGradeSelectorText('Okay, which grade level is the student in?');
      setGradeCharIndex(0);
      setDisplayGradeText('');
    } else if (selectedEntry === 'quick') {
      setInfo('You selected: Not Sure? Take a Quick Assessment');
      setShowAssessment(true);
      setAssessmentStep(0);

      setAssessmentText(assessmentQuestions[0]);
      setAssessmentCharIndex(0);
      setDisplayAssessmentText('');
      setShowAssessmentButtons(false);
      setCurrentQuestionAnimating(true);
    } else if (selectedEntry === 'survey') {
      setInfo('You selected: Parent/Teacher? Answer a Few Questions');
      setShowChat(true);

      hasReceivedEnoughContentRef.current = false;

      if (isWsConnected) {
        // sendExerciseQuery();
      } else {
        setLoading(true);

        connectToWebSocket();
      }
    }
  }, [selectedEntry, isWsConnected, connectToWebSocket]);

  // Funkcja do obsługi wyboru typu wejścia

  // Funkcja do obsługi wysyłania wiadomości
  const handleSendMessage = async () => {
    if (!message.trim()) return;

    // Ustawiamy stan ładowania na true
    setLoading(true);

    const userMessage: Message = {
      id: Date.now(),
      text: message,
      sender: 'user',
      timestamp: new Date().toISOString(),
      type: 'text',
    };
    setMessages(prev => [...prev, userMessage]);
    setMessage('');

    // Create temporary bot message that will be updated during streaming
    const tempBotMessage: Message = {
      id: Date.now() + 1,
      text: '',
      sender: 'bot_partial',
      timestamp: new Date().toISOString(),
      type: 'text',
      isStreaming: true,
    };
    setMessages(prev => [...prev, tempBotMessage]);

    const payload: ChatStreamPayload = {
      session_id: currentSessionId || '',
      domains: ['math-problem'],
      question: message,
    };

    // Handle response streaming
    let responseText = '';

    chatService.sendChatQuestion(
      payload,
      chunk => {
        const processedResponse = processResponseChunk(chunk, responseText);

        // Check if response contains interventions (new format)
        if (processedResponse.interventions && processedResponse.interventions.length > 0) {
          // Update bot message to include intervention information
          setMessages(prev => {
            const updatedMessages = [...prev];
            const botMessageIndex = updatedMessages.findIndex(msg => msg.id === tempBotMessage.id);
            if (botMessageIndex !== -1) {
              updatedMessages[botMessageIndex] = {
                ...updatedMessages[botMessageIndex],
                text: processedResponse.responseText || '',
                type: 'text',
                data: { interventions: processedResponse.interventions },
                isStreaming: false,
                sender: 'bot',
              };
            }

            return updatedMessages;
          });
          if (typeof scrollToBottom === 'function') {
            scrollToBottom();
          }
          responseText = processedResponse.responseText || '';
        }
        // Check if response contains array of games (old format)
        else if (processedResponse.games && processedResponse.games.length > 0) {
          // Update bot message to include game information
          setMessages(prev => {
            const updatedMessages = [...prev];
            const botMessageIndex = updatedMessages.findIndex(msg => msg.id === tempBotMessage.id);
            if (botMessageIndex !== -1) {
              updatedMessages[botMessageIndex] = {
                ...updatedMessages[botMessageIndex],
                text: '',
                type: 'text',
                data: { games: processedResponse.games },
                isStreaming: false,
                sender: 'bot',
              };
            }

            return updatedMessages;
          });
          if (typeof scrollToBottom === 'function') {
            scrollToBottom();
          }
          if (processedResponse && processedResponse.responseText) {
            responseText = processedResponse.responseText;
          }
        }
        // Standard text handling
        else if (processedResponse.hasNewContent) {
          responseText = processedResponse.responseText;

          // Update message in state
          setMessages(prev => {
            const updatedMessages = [...prev];
            const botMessageIndex = updatedMessages.findIndex(msg => msg.id === tempBotMessage.id);
            if (botMessageIndex !== -1) {
              updatedMessages[botMessageIndex] = {
                ...updatedMessages[botMessageIndex],
                text: responseText,
                isStreaming: true,
                sender: 'bot_partial',
              };
            }
            return updatedMessages;
          });
          scrollToBottom();
        }
      },
      async () => {
        // After streaming is complete, mark the message as finished
        setMessages(prev => {
          const updatedMessages = [...prev];
          const botMessageIndex = updatedMessages.findIndex(msg => msg.id === tempBotMessage.id);
          if (botMessageIndex !== -1) {
            updatedMessages[botMessageIndex] = {
              ...updatedMessages[botMessageIndex],
              sender: 'bot',
              isStreaming: false,
            };
          }
          return updatedMessages;
        });

        // Disable loading state after streaming is complete
        setLoading(false);
        hasReceivedEnoughContentRef.current = true;

        // Fetch and update chat history after streaming is complete
        if (currentSessionId) {
          try {
            const chatHistory = await chatService.getChatHistory(currentSessionId);

            if (Array.isArray(chatHistory) && chatHistory.length > 0) {
              // Przetwarzamy historię czatu z zachowaniem danych gier
              const formattedMessages = processMessageHistory(chatHistory as ChatHistoryItem[]);

              // Ustaw sformatowane wiadomości
              setMessages(formattedMessages);
              scrollToBottom();
            }
          } catch (error) {
            // console.error('Error while fetching chat history after sending message:', error);
          }
        }
      }
    );
  };

  // Obsługa naciśnięcia klawisza Enter
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  // Funkcja do wyboru obszaru czytania
  const handleAreaSelect = (area: ReadingArea) => {
    setSelectedReadingAreas([area]);
  };

  // Funkcja do wyboru poziomu klasy

  // Funkcja do obsługi odpowiedzi na pytania oceny (assessment)

  // Funkcja do zamykania wyników oceny
  const handleCloseAssessmentResult = () => {};

  // Funkcja do obsługi kliknięcia w podpowiedź
  const handlePromptClick = (promptId: string, promptText: string) => {
    setInfo(`Selected prompt: ${promptText}`);
  };

  // Funkcja do obsługi kliknięcia w predefiniowany prompt
  const handlePredefinedPromptClick = (promptText: string) => {
    setLoading(true);

    // Tworzymy wiadomość użytkownika z tytułem promptu
    const userMessage: Message = {
      id: Date.now(),
      text: `How can you help me with ${promptText}?`,
      sender: 'user',
      timestamp: new Date().toISOString(),
      type: 'text',
    };
    setMessages(prev => [...prev, userMessage]);

    // Create temporary bot message that will be updated during streaming
    const tempBotMessage: Message = {
      id: Date.now() + 1,
      text: '',
      sender: 'bot_partial',
      timestamp: new Date().toISOString(),
      type: 'text',
      isStreaming: true,
    };
    setMessages(prev => [...prev, tempBotMessage]);

    // Handle response streaming
    let responseText = '';

    // Wysyłamy zapytanie do API
    chatService.sendChatQuestion(
      {
        question: `How can you help me with ${promptText}?`,
        session_id: currentSessionId || '1',
        student_id: userId || '1',
      },
      chunk => {
        // Przetwarzamy odpowiedź z API
        const processedResponse = processResponseChunk(chunk, responseText);

        // Check if response contains interventions (new format)
        if (processedResponse.interventions && processedResponse.interventions.length > 0) {
          // Update bot message to include intervention information
          setMessages(prev => {
            const updatedMessages = [...prev];
            const botMessageIndex = updatedMessages.findIndex(msg => msg.id === tempBotMessage.id);
            if (botMessageIndex !== -1) {
              updatedMessages[botMessageIndex] = {
                ...updatedMessages[botMessageIndex],
                text: processedResponse.responseText || '',
                type: 'text',
                data: { interventions: processedResponse.interventions },
                isStreaming: false,
                sender: 'bot',
              };
            }
            return updatedMessages;
          });
          scrollToBottom();
          responseText = processedResponse.responseText || '';
        }
        // Check if response contains array of games (old format)
        else if (processedResponse.games && processedResponse.games.length > 0) {
          // Update bot message to include game information
          setMessages(prev => {
            const updatedMessages = [...prev];
            const botMessageIndex = updatedMessages.findIndex(msg => msg.id === tempBotMessage.id);
            if (botMessageIndex !== -1) {
              updatedMessages[botMessageIndex] = {
                ...updatedMessages[botMessageIndex],
                text: '',
                type: 'text',
                data: { games: processedResponse.games },
                isStreaming: false,
                sender: 'bot',
              };
            }
            return updatedMessages;
          });
          scrollToBottom();
          responseText = processedResponse.responseText || '';
        }
        // Standard text handling
        else if (processedResponse.hasNewContent) {
          responseText = processedResponse.responseText;

          // Aktualizujemy wiadomość bota z nowym tekstem
          setMessages(prev => {
            const updatedMessages = [...prev];
            const botMessageIndex = updatedMessages.findIndex(msg => msg.id === tempBotMessage.id);
            if (botMessageIndex !== -1) {
              updatedMessages[botMessageIndex] = {
                ...updatedMessages[botMessageIndex],
                text: responseText,
              };
            }
            return updatedMessages;
          });
          scrollToBottom();
        }
      },
      () => {
        // After streaming is complete, mark the message as finished
        setMessages(prev => {
          const updatedMessages = [...prev];
          const botMessageIndex = updatedMessages.findIndex(msg => msg.id === tempBotMessage.id);
          if (botMessageIndex !== -1) {
            updatedMessages[botMessageIndex] = {
              ...updatedMessages[botMessageIndex],
              sender: 'bot',
              isStreaming: false,
            };
          }
          return updatedMessages;
        });

        // Disable loading state after streaming is complete
        setLoading(false);
        hasReceivedEnoughContentRef.current = true;
      }
    );
  };

  // Funkcja do obsługi wysyłania ćwiczeń matematycznych
  const handleMathExercisesSubmit = (payload: {
    age: number;
    grade: string;
    domains: string[];
    selectedArea: string;
  }) => {
    setLoading(true);

    // Tworzymy obszar Math Problem
    const mathArea = {
      id: 999,
      name: 'Math Problem',
      description: 'Exercises for practicing math concepts and problem-solving skills.',
    };

    // Ustawiamy wybrany obszar
    setSelectedReadingAreas([mathArea]);

    handleExercisesSubmit(
      payload,
      details => {
        setSelectedAreaDetails(details);
        setLoading(false);
      },
      () => {
        setLoading(false);
      }
    );
  };

  return (
    <div className="h-full bg-white rounded-xl py-6">
      <div className="flex items-center gap-4 border-b border-gray-200 pb-4 mb-4 px-6">
        <div className="w-12 h-12 rounded-full bg-[#E3F2FD] flex items-center justify-center text-[#1976D2] text-xl font-medium">
          {loadingStudent
            ? '...'
            : propStudentName?.[0]?.toUpperCase() ||
              studentData?.first_name?.[0]?.toUpperCase() ||
              ''}
        </div>
        <div>
          <h1 className="text-xl font-medium text-gray-900">
            {loadingStudent && !propStudentName
              ? 'Loading...'
              : propStudentName ||
                (studentData
                  ? `${studentData.first_name} ${studentData.last_name}`
                  : userId
                    ? `Student ${userId}`
                    : 'Unknown student')}
          </h1>
        </div>
      </div>

      <div className="h-[calc(100vh-220px)] flex flex-col">
        <div className="flex-1 overflow-y-auto">
          {loading && messages.length === 0 ? (
            <div className="text-center text-gray-500 my-8">Loading messages...</div>
          ) : messages.length === 0 && !showChat ? (
            <div className="my-8">
              <div className="flex items-center mb-6 h-8 mt-4 pl-16">
                <img src={stars} alt="" className="w-6 h-6 mr-2 ml-[13px]" />
                <p className="text-xl font-semibold text-gray-700">{welcomeText}</p>
              </div>

              <PredefinedPrompts onPromptClick={handlePredefinedPromptClick} />

              <ReadingAreaSelector
                readingAreas={readingAreas}
                selectedReadingAreas={selectedReadingAreas}
                displayAreaText={displayAreaText}
                areaSelectorText={areaSelectorText}
                loadingReadingAreas={loadingReadingAreas}
                handleAreaSelect={handleAreaSelect}
                handleExercisesSubmit={handleMathExercisesSubmit}
                selectedGrade={selectedGrade}
              />

              {selectedEntry === 'quick' && assessmentResult && (
                <div ref={assessmentResultRef}>
                  <AssessmentDetailsComponent
                    result={assessmentResult}
                    onClose={handleCloseAssessmentResult}
                    onPromptClick={handlePromptClick}
                    onEdit={() => {
                      setSelectedEntry('quick');
                      setAssessmentStep(0);
                      setAnsweredQuestions([]);
                      setAssessmentResult(null);
                      setAssessmentText(assessmentQuestions[0]);
                      setAssessmentCharIndex(0);
                      setDisplayAssessmentText('');
                      setShowAssessmentButtons(false);
                      setCurrentQuestionAnimating(true);
                      setShowAssessment(true);
                    }}
                  />
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-4 p-4">
              {messages.map(msg => (
                <ChatMessage
                  key={msg.id}
                  message={msg}
                  studentId={userId ? parseInt(userId, 10) : undefined}
                />
              ))}
              {loading && messages.length > 0 && (
                <div className="flex justify-center items-center py-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#005773]"></div>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>

        {error && <div className="text-red-500 text-sm text-center mb-2">{error}</div>}

        <ChatInput
          message={message}
          setMessage={setMessage}
          handleKeyPress={handleKeyPress}
          handleSendMessage={handleSendMessage}
          loading={loading}
          hasReceivedEnoughContent={hasReceivedEnoughContentRef.current}
          selectedEntry={selectedEntry}
          isWsConnected={isWsConnected}
        />
      </div>
    </div>
  );
};

export default ChatPage;
